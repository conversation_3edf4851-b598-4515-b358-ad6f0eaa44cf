# AI Prompt Chaining Manager

A web application for managing and evaluating AI prompt chains using Google's Gemini AI service.

## Features

- **Prompt Management**: Edit and version two prompts that form a chain
- **Static Flow**: Input → Prompt 1 → Gemini → Output 1 → Prompt 2 → Gemini → Final Output
- **Evaluation History**: Track all evaluation runs with timestamps and prompt versions
- **File-based Storage**: Simple JSON file storage for prompts and evaluations
- **Real-time UI**: React frontend with immediate feedback

## Project Structure

```
prompt-evals/
├── .env                    # Environment variables (Gemini API key)
├── package.json           # Root package.json for scripts
├── server/                # Backend (Node.js + Express)
│   ├── server.js         # Main server file
│   ├── routes/           # API routes
│   ├── services/         # Gemini AI service
│   └── data/             # JSON file storage
└── client/               # Frontend (React + Vite)
    ├── src/
    │   ├── components/   # React components
    │   └── services/     # API client
    └── public/
```

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- Google Gemini API key

### Installation

1. **Install all dependencies:**
   ```bash
   npm run install-all
   ```

2. **Verify environment variables:**
   Make sure `.env` file contains your Gemini API key:
   ```
   GEMINI_API_KEY=your_api_key_here
   ```

3. **Start the application:**
   ```bash
   npm run dev
   ```

   This will start both the backend server (port 3001) and frontend development server (port 3000).

4. **Access the application:**
   Open your browser and go to `http://localhost:3000`

### Individual Commands

- **Start backend only:** `npm run server`
- **Start frontend only:** `npm run client`
- **Install backend deps:** `cd server && npm install`
- **Install frontend deps:** `cd client && npm install`

## Usage

1. **Edit Prompts**: Use the prompt editors to modify the two prompts in your chain
2. **Run Evaluations**: Enter input text and click "Run Prompt Chain" to execute the flow
3. **View Results**: Check the results table to see evaluation history with prompt versions

## API Endpoints

### Prompts
- `GET /api/prompts` - Get all prompts
- `GET /api/prompts/:id` - Get specific prompt
- `PUT /api/prompts/:id` - Update prompt (creates new version)

### Evaluations
- `GET /api/evaluations` - Get all evaluation results
- `POST /api/evaluations/run` - Run new evaluation

## Data Storage

- **Prompts**: Stored in `server/data/prompts.json`
- **Evaluations**: Stored in `server/data/evaluations.json`

Both files are automatically created with default data on first run.

## Technologies Used

- **Backend**: Node.js, Express, Google Generative AI SDK
- **Frontend**: React, Vite, Axios
- **AI Service**: Google Gemini 1.5 Flash
- **Storage**: File-based JSON storage
