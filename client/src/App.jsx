import React, { useState, useEffect } from 'react';
import PromptEditor from './components/PromptEditor';
import EvaluationRunner from './components/EvaluationRunner';
import ResultsTable from './components/ResultsTable';
import { promptsApi, evaluationsApi } from './services/api';

const App = () => {
  const [prompts, setPrompts] = useState([]);
  const [evaluations, setEvaluations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [promptsResponse, evaluationsResponse] = await Promise.all([
        promptsApi.getAll(),
        evaluationsApi.getAll()
      ]);
      setPrompts(promptsResponse.data);
      setEvaluations(evaluationsResponse.data);
      setError(null);
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePromptUpdate = async (id, content) => {
    try {
      const response = await promptsApi.update(id, content);
      setPrompts(prev => prev.map(p => p.id === id ? response.data : p));
      return response.data;
    } catch (err) {
      console.error('Error updating prompt:', err);
      throw err;
    }
  };

  const handleEvaluationRun = async (input) => {
    try {
      const response = await evaluationsApi.run(input);
      setEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running evaluation:', err);
      throw err;
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <h2>Error: {error}</h2>
        <button onClick={loadData}>Retry</button>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>
        AI Prompt Chaining Manager
      </h1>

      <div style={{ display: 'grid', gap: '20px', marginBottom: '30px' }}>
        <div
          className="prompt-grid"
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '20px'
          }}>
          {prompts.map(prompt => (
            <PromptEditor
              key={prompt.id}
              prompt={prompt}
              onUpdate={handlePromptUpdate}
            />
          ))}
        </div>

        <EvaluationRunner onRun={handleEvaluationRun} />
      </div>

      <ResultsTable evaluations={evaluations} />
    </div>
  );
};

export default App;
